---
type: "always_apply"
---

# Reglas de Git

## Estrategia Branching

### Branches Principales

- **main**: Produccion (codigo estable, deployable)
- **develop**: Integracion de desarrollo
- **feature/[task-id]-description**: Nuevas funcionalidades
- **bugfix/[task-id]-description**: Correccion bugs
- **hotfix/[task-id]-description**: Fixes urgentes produccion

### Flujo Individual

1. **Iniciar**: Actualizar develop, crear branch feature
2. **Desarrollar**: Commits atomicos con formato estandar
3. **Integrar**: Rebase con develop, push a remoto
4. **Finalizar**: Merge no-fast-forward, limpiar branch

### Mejores Practicas

- Pull antes de push siempre
- Commits atomicos (un cambio logico)
- No commits con codigo roto
- Self-review antes de push
- Branches vida corta (<1 semana)
- Rebase para actualizar, merge para integrar

## Formato Commits

### Estandar Obligatorio

- **Idioma**: Ingles
- **Formato**: `YYYY-MM-DD HH:MM:SS AM|PM [tipo] T-XXX: Descripcion`
- **Ejemplo**: `2024-01-15 02:30:45 PM [feat] T-001: Add user authentication`

### Tipos Commits

- `[feat]`: Nueva funcionalidad
- `[fix]`: Correccion bug
- `[docs]`: Solo documentacion
- `[style]`: Formato (no afecta logica)
- `[refactor]`: Refactoring codigo
- `[test]`: Tests nuevos/modificados
- `[chore]`: Mantenimiento
- `[perf]`: Mejoras performance
- `[security]`: Fixes seguridad

### Triggers Automaticos

1. Cada 2 horas trabajo activo
2. Completar tarea (T-XXX)
3. Completar subtarea (ST-XXX)
4. Antes cambiar contexto
5. Finalizar jornada

### Que Commitear

- **Si**: Feature completa, bug fix funcional, refactor modulo, config importante, tests
- **No**: Espacios/tabs, console.logs debug, codigo comentado, archivos temporales

### Descripcion Commits

- **Buenas**: "Add OAuth2 integration", "Fix validation error"
- **Malas**: "Fix bug", "Updates", "WIP", texto sin sentido

## Documentacion Cambios

### CHANGELOG.md (Publico)

- **Proposito**: Cambios importantes por version para usuarios
- **Formato**: Keep a Changelog + Semantic Versioning
- **Secciones**: Added, Fixed, Changed, Security, Deprecated, Removed
- **Versionado**: [1.2.0] - 2024-01-15

### gitlog.md (Privado)

- **Proposito**: Registro detallado todos commits para desarrollador
- **Ubicacion**: .gitignore (no commitear)
- **Organizacion**: Cronologica por fecha
- **Formato**: `- YYYY-MM-DD HH:MM:SS AM|PM [tipo] T-XXX: descripcion`

## Automatizacion

### Scripts Automatizacion

- **Auto-commit**: Commits automaticos con formato estandar
- **Timer 2h**: Sistema recordatorio commits progreso
- **Changelog**: Actualizacion basada en tipos commit
- **Gitlog**: Generacion post-commit via hooks

### Git Hooks

- **Pre-commit**: Verificar console.log, ejecutar linter/tests
- **Post-commit**: Actualizar gitlog.md automaticamente
- **Commit-msg**: Validar formato mensaje commit

### Release Process

1. Actualizar CHANGELOG.md (Unreleased → version)
2. Crear tag version (v1.2.0)
3. Commit release con formato estandar
4. Push cambios y tags

## Configuracion Setup

### Configuracion Global

- **user.name/user.email**: Identidad commits
- **core.editor**: Editor para mensajes
- **core.autocrlf**: true (Windows)
- **init.defaultBranch**: main

### .gitignore Base

- **Dependencies**: node_modules/, vendor/
- **Environment**: .env, .env.local
- **IDE**: .vscode/, .idea/
- **Build**: dist/, build/, *.log
- **Testing**: coverage/
- **Project**: gitlog.md, *.tmp

## Resolucion Conflictos

### Proceso Estandar

1. **Identificar**: git status muestra archivos conflicto
2. **Resolver**: Editar archivos, elegir version correcta
3. **Marcar**: git add archivos resueltos
4. **Continuar**: git merge --continue o git rebase --continue

### Estrategias Merge

- **Normal**: Crea commit merge (historia preservada)
- **Fast-forward**: Historia lineal
- **No-fast-forward**: Preserva historia branch
- **Rebase**: Reescribe historia limpia

## Integracion Augment Code

### Comandos IA

- **Iniciar tarea**: Checkout branch feature/T-XXX
- **Completar cambio**: Auto-commit con tipo apropiado
- **Recordatorio 2h**: Commit progreso actual
- **Finalizar**: Merge develop, actualizar CHANGELOG

### Estadisticas Proyecto

- **Por tipo**: Features, fixes, tests, docs
- **Por tarea**: Tracking T-XXX completadas
- **Temporal**: Actividad dia/semana/mes
- **Productividad**: Commits vs tareas completadas
