---
type: "always_apply"
---

# Reglas de Testing

## Filosofia Testing

### Principio Fundamental

**Probar funcionalidades completas, no lineas codigo**

- Enfoque flujos usuario end-to-end
- Validacion valor negocio, no metricas cobertura
- Tests fallen cuando funcionalidad realmente rota

## Tipos Tests por Prioridad

### 1. Tests Integracion (Alta Prioridad)

- **Proposito**: Flujos completos usuario
- **Scope**: API hasta base datos
- **Ejemplos**: Registro usuario, proceso compra, auth
- **Estructura**: Arrange → Act → Assert
- **Validation**: Respuesta correcta, datos persistidos, efectos secundarios

### 2. Tests End-to-End (E2E)

- **Proposito**: Simulacion interaccion real usuario
- **Herramientas**: Playwright, Cypress, Selenium
- **Scope**: Frontend + Backend + Base datos
- **Flujos criticos**: <PERSON>gin, compra, registro, operaciones principales
- **Validation**: UI funcional, navegacion correcta, datos visibles

### 3. Tests API

- **Proposito**: Validar contratos respuestas endpoints
- **Scope**: Request/Response, status codes, data structure
- **Casos**: Happy path, error handling, input validation
- **Validation**: Schema compliance, status apropiados, data integrity

## Organizacion y Estructura

### Estructura Directorios

```
tests/
├── integration/     # Flujos completos por feature
├── e2e/            # End-to-end por user journey
├── api/            # Endpoints por recurso
├── fixtures/       # Datos prueba reutilizables
└── helpers/        # Utilidades testing compartidas
```

### Convenciones Naming

- **Archivos**: feature-name.test.js, user-flow.test.js
- **Describes**: Funcionalidad o endpoint
- **Tests**: "should [behavior] when [condition]"
- **Variables**: Descriptivas proposito test

## Datos de Prueba

### Fixtures y Test Data

- **Usuarios prueba**: Diferentes roles estados
- **Dominio datos**: Productos, ordenes, configuraciones
- **Edge cases**: Datos limite, invalidos, vacios
- **Isolation**: Cada test datos independientes

### Database Testing Strategy

- **Setup**: Crear test DB, migrations, seeds
- **Isolation**: Transaccion o limpiar entre tests
- **Teardown**: Limpiar despues test suite
- **Reset**: Metodo volver estado limpio

## Integracion IA

### Generacion Automatica Tests

- **Input**: Descripcion funcionalidad user story
- **Output**: Tests estructurados casos principales
- **Incluye**: Happy path, error handling, edge cases
- **Formato**: Arrange-Act-Assert pattern

### Validacion IA

- **Analysis**: Revisar funcionalidad cumple criterios
- **Criterios**: Tests pasan, no errores console, performance ok
- **Output**: Validacion boolean, issues, sugerencias mejora
- **Iteration**: Corregir issues, re-validar hasta aprobacion

## Scripts y Comandos

### NPM Scripts Estandar

- **test**: Suite completo
- **test:integration**: Solo integration tests
- **test:e2e**: Solo end-to-end tests
- **test:api**: Solo API tests
- **test:watch**: Watch mode desarrollo
- **test:ci**: Optimizado CI/CD

### Execution Modes

- **Development**: Watch mode hot reload
- **CI/CD**: Run completo reporting
- **Debug**: Individual verbose output

## Criterios Aceptacion

### Feature Checklist

- **Functionality**: Happy path, input validation, error handling, edge cases
- **Performance**: Response <1s, no memory leaks
- **UI/UX**: No console errors, responsive, accessible
- **Documentation**: Tests documented, use cases clear

### Definition Done

- Integration tests implemented passing
- E2E tests flujos criticos
- API tests endpoints nuevos/modificados
- Performance limites aceptables
- Sin regresiones funcionalidad existente

## Reporting Monitoreo

### Testing Metrics

- **Functional coverage**: User stories con tests porcentaje
- **Success rate**: Tests passing vs failing ratio
- **Execution time**: Test suite tiempo total
- **Flaky tests**: Tests fallan intermitentemente

### Reports Structure

- **Summary**: Total, passed, failed, duration
- **Por feature**: Status, tests, coverage
- **Trends**: Metrics evolucion tiempo
- **Failures**: Detalles fallos debugging

## CI/CD Integration

### Testing Pipeline

- **Pre-commit**: Unit tests rapidos
- **Pull request**: Test suite completo
- **Merge**: Regression tests
- **Deploy**: Smoke tests ambiente

### Pipeline Config

- **Parallelization**: Tests paralelo cuando posible
- **Retry logic**: Re-ejecutar flaky automatico
- **Failure handling**: Fail build tests criticos
- **Reporting**: Resultados PR comments

## Best Practices

### Test Design

- **Independence**: Test ejecuta solo
- **Repeatability**: Mismos resultados multiples runs
- **Fast feedback**: Tests rapidos development loop
- **Clear assertions**: Error messages informativos

### Maintenance

- **DRY principle**: Reutilizar helpers fixtures
- **Single responsibility**: Test por behavior
- **Meaningful names**: Explican que prueba
- **Regular cleanup**: Eliminar tests obsoletos

### Data Management

- **Isolation**: Sin dependencias entre tests
- **Cleanup strategy**: Limpiar consistente
- **Environment separation**: Datos por ambiente
- **Sensitive data**: Mock anonymizar sensible

## Integration Specific

### Augment Code Testing

- **Auto-generation**: IA crea tests funcionalidad descrita
- **Validation loop**: IA valida cumplimiento criterios
- **Feedback integration**: Mejora iterativa hasta aprobacion
- **Documentation**: Tests self-documenting con IA
