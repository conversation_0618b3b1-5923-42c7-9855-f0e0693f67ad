---
type: "always_apply"
---

# Reglas de Codigo

## Nomenclatura y Convenciones

### Idioma del Codigo

- **Todo en ingles**: Variables, funciones, clases, comentarios sin excepciones
- **Solo documentacion usuario final** puede estar en español

### Por Tipo de Elemento

- **Variables/Funciones**: camelCase (userName, calculateTotal, getUserById)
- **Clases**: PascalCase (UserController, AuthenticationService)  
- **Constantes**: UPPER_SNAKE_CASE (MAX_RETRY_ATTEMPTS, API_BASE_URL)
- **Archivos/Carpetas**: kebab-case (user-service.js, /api-routes/)
- **Componentes**: PascalCase (UserProfile.jsx, AuthForm.vue)

### Convenciones Especificas

- **Booleanos**: Prefijos descriptivos (isActive, hasPermission, canEdit)
- **Eventos**: Prefijos on/handle (onClick, handleSubmit)
- **Getters/Setters**: get/set prefijos (getUserName, setConfiguration)
- **Metodos privados**: Prefijo underscore (_validateToken)
- **Async functions**: Sufijo opcional Async segun lenguaje

### Base de Datos

- **Tablas**: snake_case plural (users, user_profiles)
- **Columnas**: snake_case (user_id, created_at, is_active)
- **Indices**: tabla_columna_idx (users_email_idx)

### Variables de Entorno

- **Formato**: UPPER_SNAKE_CASE con prefijos (DB_HOST, API_KEY)
- **Agrupacion**: Por servicio o funcionalidad

## Limites y Estructura de Archivos

### Limite Principal: 400 Lineas

- **Justificacion**: Contexto optimo IA (8,000-10,000 tokens)
- **Beneficios**: Una responsabilidad, facil testing, mejor review

### Excepciones Permitidas con Criterios

- **Configuracion**: Sin limite (config pura, no logica)
- **Tests**: Maximo 800 lineas, divir si >5 test suites diferentes
- **Tipos/Interfaces**: Maximo 200 lineas, separar por dominio si excede
- **Index files**: Maximo 50 lineas, solo exports y re-exports

### Señales Refactorizacion Obligatorias

- **250 lineas**: Revisar modularizacion posible
- **350 lineas**: Planificar division (próximo sprint)
- **400 lineas**: Division obligatoria inmediata
- **>10 metodos clase**: Dividir responsabilidades
- **>50 lineas funcion**: Extraer subfunciones
- **>3 niveles anidamiento**: Extraer funciones helper

## Documentacion en Codigo

### Principio: Documenta el POR QUE

- **Evitar**: Codigo obvio, redundancia con nombres
- **Incluir**: Decisiones negocio, workarounds, configuraciones especiales

### Tipos de Comentarios

- **Una linea**: TODO(autor), FIXME(ticket), HACK, NOTE
- **Funciones**: Proposito, parametros, retorno, ejemplos, excepciones
- **Clases**: Responsabilidad, relaciones, constructor
- **APIs**: Metodo HTTP, parametros, respuestas, ejemplos
- **Headers**: Archivo, descripcion, dependencias, autor

### Documentacion Configuracion

- **Archivos .env**: Agrupacion logica, defaults, ejemplos
- **Contexto uso**: Cuando aplicar cada configuracion

## Manejo de Errores

### Proceso 4 Pasos

1. **Analisis**: 3 parrafos (contexto, causas, relaciones)
2. **Explicacion**: Que/por que falla, impacto, solucion
3. **Implementacion**: Cambios minimos necesarios, documentar
4. **Testing**: Reproducir, casos edge, confirmacion

### Logging Estructurado

- **Niveles**: ERROR (critico), WARN (manejado), INFO (eventos), DEBUG (detalle)
- **Contexto**: Timestamp, usuario, request ID, metadata

### Estrategias Retry

- **Red**: 3 intentos con backoff exponencial
- **Validacion**: 1 intento (fail fast)
- **DB locks**: 5 intentos
- **Auth**: 0 reintentos

### Clasificacion Errores

- **Recuperables**: Network, rate limits, DB locks (retry)
- **No recuperables**: Validation, auth, config (fail fast)

## Modularidad y Arquitectura

### Principio SOLID

- **Una responsabilidad** por modulo/clase/funcion
- **Dependency injection** sobre hard-coding
- **Interfaces** para abstracciones
- **Composition** sobre herencia

### Patrones Recomendados

- **Repository**: Abstraccion acceso datos
- **Factory**: Creacion objetos segun tipo
- **Strategy**: Algoritmos intercambiables
- **Observer**: Comunicacion desacoplada

### Organizacion Modulos

- **Por capa**: controllers/services/repositories (tradicional)
- **Por feature**: auth/, users/, payments/ (moderno)
- **Hibrida**: Segun complejidad proyecto

### Reglas Modularidad

1. **Maximo 1 clase** principal por archivo
2. **Interfaces separadas** de implementaciones
3. **Sin importaciones circulares**
4. **Maximo 5 dependencias** por modulo
5. **Tests paralelos** a estructura codigo

### Comunicacion Entre Modulos

- **Event Emitter**: Desacoplamiento via eventos
- **Dependency Injection**: Servicios compartidos
- **Exports centralizados**: Index.js por modulo

## Integration Patterns

### Servicios Externos

- **API Wrappers**: Una clase por servicio externo
- **Adapter Pattern**: Interfaces consistentes independiente proveedor
- **Circuit Breaker**: Fallos >5 consecutive = circuit abierto 60s
- **Timeout**: Maximo 30s calls externos, 5s criticos
- **Retry**: Exponential backoff, max 3 intentos

### Third Party APIs

- **Rate Limiting**: Respetar limites provider, buffer 20%
- **Authentication**: Refresh tokens automatico antes expiracion
- **Error Handling**: Map errores externos a errores internos
- **Logging**: Request/response IDs para debugging
- **Fallback**: Servicio backup o graceful degradation

### Data Integration

- **ETL Patterns**: Extract → Transform → Load con validacion cada paso
- **Schema Validation**: Validar estructura antes procesamiento
- **Idempotency**: Operaciones repetibles sin efectos secundarios
- **Conflict Resolution**: Last-write-wins o merge strategies definidas

## Checklist Calidad Codigo

### Estructura

- Una sola responsabilidad por archivo
- Nombres descriptivos y consistentes
- Dependencias inyectadas, no hardcodeadas
- Sin estado global compartido

### Documentacion

- Comentarios explican "por que", no "que"
- Headers de archivo completos
- Ejemplos de uso en funciones complejas
- Configuracion documentada

### Testing

- Tests paralelos a estructura codigo
- Nomenclatura descriptiva del comportamiento
- Casos normales, edge cases, errores
- Isolation entre tests

### Integration

- External services con circuit breakers
- Timeouts apropiados configurados
- Error mapping consistente
- Fallback strategies implementadas
