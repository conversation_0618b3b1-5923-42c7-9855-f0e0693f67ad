---
type: "always_apply"
---

# Reglas de Environment

## Ambiente Windows 11

### Sistema Base

- **WSL habilitado**: Compatibilidad herramientas Linux
- **PowerShell policy**: RemoteSigned scripts locales
- **Windows Terminal**: Principal con perfil personalizado
- **Variables entorno**: Usuario y sistema configuradas

### Herramientas Esenciales

- **IDE**: VS Code + extensiones (Augment Code, ESLint, Prettier, TypeScript, Tailwind)
- **Git**: Configuracion global autocrlf=true Windows
- **Node.js**: Via Volta gestion versiones
- **Databases**: PostgreSQL, MongoDB + DBeaver GUI
- **Docker Desktop**: Contenedorizacion servicios locales

### Scripts PowerShell

- **Project init**: Estructura, Git init, archivos base
- **DB backup**: Dump automatizado compresion
- **Dev aliases**: Git, <PERSON><PERSON>, npm shortcuts profile
- **Navigation**: Cambio rapido proyectos

## Multi-Sistema Config

### Multi-Idioma (i18n)

- **Idiomas**: Array locales definido (en, es, pt, fr)
- **Deteccion**: Cookie → header → querystring orden
- **Archivos**: JSON por idioma namespaces (common, errors)
- **Fallback**: Idioma default traduccion no existe

### Multi-Zona Horaria

- **Backend UTC**: Fechas almacenadas UTC siempre
- **Frontend conversion**: Zona usuario display
- **Input handling**: Local → UTC antes guardar
- **User config**: Timezone, formato fecha/hora

### Multi-Moneda

- **Config**: Simbolo, decimales, posicion por moneda
- **Conversion**: API tipos cambio actualizados
- **Formateo**: Segun config moneda localizacion
- **Storage**: Moneda base + original

## Modular Design

### Module Structure

- **Por feature**: auth/, users/, payments/, notifications/
- **Self-contained**: Routes, controller, service, repository, tests
- **Shared**: Database, cache, utils compartidos
- **Core**: Config middleware central

### Dynamic Loading

- **Module loader**: Scan automatico directorio modules/
- **Route registration**: Automatico naming conventions
- **Initialization**: Hook init() modulo existe
- **Hot reload**: Desarrollo cambios modulos

## Variables Ambiente

### Configuration Management

- **Template**: .env.example todas variables necesarias
- **Separation**: .env.local, .env.staging, .env.production
- **Critical vars**: NODE_ENV, DB credentials, JWT secrets
- **Documentation**: Comments proposito variable

### Security

- **Secrets**: Variables entorno, nunca hardcode
- **Rotation**: Keys passwords cambio regular
- **Access**: Solo personal autorizado production
- **Encryption**: Secrets encrypted repos privados

## Deployment CI/CD

### Deployment Strategy

- **Containerization**: Docker multistage builds
- **Pipeline**: Test → Build → Deploy GitHub Actions
- **Rollback**: Version anterior disponible
- **Zero downtime**: Blue-green o rolling

### Version Control

- **Semantic**: major.minor.patch tipo cambios
- **Git flow**: Feature branches, develop, main
- **Release notes**: Automaticos commits tags
- **Hotfix**: Path rapido fixes criticos

## Monitoring Debug

### Logging

- **Structured**: Debug, info, warn, error contexto
- **Centralized**: ELK, Splunk agregacion
- **JSON format**: Parsing analisis automatizado
- **Retention**: Limpieza automatica logs antiguos

### Health Checks

- **Endpoints**: /health, /ready, /metrics monitoring
- **Dependencies**: Database, Redis, external APIs
- **Performance**: Response time, memory, CPU
- **Business**: User signups, transactions, revenue

### Debugging

- **VS Code**: Launch.json debugging breakpoints
- **Debug logging**: Granular namespaces (app:auth, app:db)
- **Environment**: Different debug levels ambiente
- **Error tracking**: Sentry crash reporting

## Simplified Installation

### Automated Setup

- **Install script**: Bash/PowerShell verifica requirements
- **Docker compose**: Dev services (DB, Redis)
- **Seed data**: Inicial desarrollo testing
- **Build process**: Assets compilation

### Documentation

- **README**: Prerequisites, installation, usage detallado
- **Setup**: Step-by-step nuevos developers
- **Troubleshooting**: Issues comunes soluciones
- **Architecture**: Diagrama componentes flujo

## Scalability

### Performance

- **Caching**: Redis sessions, queries, computed data
- **DB optimization**: Indices, query optimization, pooling
- **CDN**: Static assets servidos CDN
- **Load balancing**: Distribucion instancias

### Advanced Monitoring

- **APM tools**: New Relic, DataDog performance
- **Log analysis**: Busqueda alertas patterns
- **Resource monitoring**: CPU, memory, disk, network
- **User analytics**: Behavior tracking conversion

## Environment Checklist

### Development

- IDE configurado extensiones necesarias
- Git credenciales configuradas
- Node.js/Python version correcta
- Variables entorno definidas
- Database local funcionando
- Docker servicios running

### Production

- Variables entorno production configuradas
- SSL certificates validos
- Backup automatizado configurado
- Monitoring alertas activas
- Load balancer configurado
- CDN pointing correctly
- Database migrations applied
- Health checks responding
